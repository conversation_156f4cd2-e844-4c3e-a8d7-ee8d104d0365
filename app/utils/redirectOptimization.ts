/**
 * 重定向优化工具函数
 * 用于减少登录后的重定向次数和提升用户体验
 */

import { getUserFirstWorkspace } from '@/app/actions/workspace';

/**
 * 获取最优的重定向URL，避免通过根页面的额外重定向
 * @param fallbackUrl 备用URL，通常是Auth.js返回的URL或callbackUrl
 * @returns 最优的重定向URL
 */
export async function getOptimalRedirectUrl(fallbackUrl: string): Promise<string> {
  // 如果fallbackUrl不是根路径，直接使用（用户可能有特定的目标页面）
  if (fallbackUrl !== '/') {
    return fallbackUrl;
  }

  try {
    // 尝试获取用户的第一个工作空间，避免通过根页面重定向
    const firstWorkspaceResult = await getUserFirstWorkspace();
    
    if (firstWorkspaceResult.status === 'success' && firstWorkspaceResult.data) {
      // 直接跳转到用户的第一个工作空间的聊天页面
      return `/${firstWorkspaceResult.data.workspaceId}/chat`;
    } else if (firstWorkspaceResult.status === 'fail') {
      // 数据库查询失败，跳转到工作区选择页面
      return '/workspaces';
    } else {
      // 用户没有工作空间，跳转到引导页面
      return '/onboarding';
    }
  } catch (error) {
    console.error('Error getting optimal redirect URL:', error);
    // 发生错误时使用fallback，确保用户不会卡在登录页面
    return fallbackUrl;
  }
}

/**
 * 预加载用户工作空间信息（可用于进一步优化）
 * @returns 工作空间信息或null
 */
export async function preloadUserWorkspace() {
  try {
    const result = await getUserFirstWorkspace();
    return result.status === 'success' ? result.data : null;
  } catch (error) {
    console.error('Error preloading user workspace:', error);
    return null;
  }
}

/**
 * 检查URL是否需要重定向优化
 * @param url 要检查的URL
 * @returns 是否需要优化
 */
export function shouldOptimizeRedirect(url: string): boolean {
  // 只对根路径进行优化，其他路径保持原样
  return url === '/' || url === '';
}
