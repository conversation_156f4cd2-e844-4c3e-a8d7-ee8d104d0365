'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button, Typography, Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { signOut, useSession } from 'next-auth/react';

const { Text } = Typography;

interface HeaderProps {
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 登出后的回调 URL，默认为 '/login'
   */
  signOutCallbackUrl?: string;
  /**
   * 是否显示用户邮箱前缀，默认为 true
   */
  showUserEmail?: boolean;
}

/**
 * 通用 Header 组件
 * 包含 logo、品牌名称、用户信息和登出功能
 * 支持响应式设计
 */
const Header: React.FC<HeaderProps> = ({
  className = '',
  signOutCallbackUrl = '/login',
  showUserEmail = true,
}) => {
  const { data: session } = useSession();

  const handleSignOut = async () => {
    await signOut({ callbackUrl: signOutCallbackUrl });
  };

  // 创建下拉菜单项
  const menuItems = [
    {
      key: 'signout',
      label: '退出',
      onClick: handleSignOut,
    },
    // 预留空间给将来的功能
    // {
    //   key: 'profile',
    //   icon: <UserOutlined />,
    //   label: '个人资料',
    //   onClick: () => console.log('个人资料'),
    // },
    // {
    //   key: 'settings',
    //   icon: <SettingOutlined />,
    //   label: '设置',
    //   onClick: () => console.log('设置'),
    // },
  ];

  return (
    <header className={`w-full bg-white border-b ${className}`}>
      <div className="container mx-auto px-4 sm:px-6 py-4 max-w-6xl">
        <div className="flex justify-between items-center">
          {/* Logo 和品牌名称 */}
          <Link href='/workspaces' className='hover:text-gray-800 text-gray-600'>
            <div className="flex items-center">
              <Image
                src='/images/logo.png'
                alt={`HiveChat Logo`}
                width={28}
                height={28}
                className="mr-2"
              />
              <span className="mb-0 text-xl t font-medium">
                HiveChat
              </span>
            </div>
          </Link>
          {/* 用户信息和下拉菜单 */}
          <div className="flex items-center">
            {session?.user?.email && (
              <Dropdown
                menu={{ items: menuItems }}
                trigger={['click']}
                placement="bottomRight"
                overlayClassName="min-w-32"
              >
                <div className="flex items-center space-x-1 cursor-pointer px-2 py-1 rounded hover:bg-gray-50 transition-colors">
                  {showUserEmail && (
                    <Text type="secondary" className="text-xs sm:text-sm">
                      {session.user.email}
                    </Text>
                  )}
                  <DownOutlined className="text-gray-400 text-xs" />
                </div>
              </Dropdown>
            )}
            {/* 如果没有用户信息，显示原来的登出按钮作为备用 */}
            {!session?.user?.email && (
              <Button
                onClick={handleSignOut}
                type="text"
                size="small"
                className="text-gray-500 hover:text-gray-700"
                title="登出"
              />
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
