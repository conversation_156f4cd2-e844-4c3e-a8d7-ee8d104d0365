'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Dropdown, Button, Typography, Spin } from 'antd';
import { DownOutlined, PlusOutlined, CheckOutlined, InteractionOutlined } from '@ant-design/icons';
import { useRouter, useParams } from 'next/navigation';
import { getUserWorkspaces, getWorkspaceInfo } from '@/app/actions/workspace';
import type { MenuProps } from 'antd';

const { Text } = Typography;

interface WorkspaceInfo {
  id: string;
  name: string;
  owner: string | null;
  plan: 'free' | 'plus' | 'pro';
  role: 'owner' | 'admin' | 'member';
  createdAt: Date;
  updatedAt: Date;
}

interface WorkspaceSelectorProps {
  /**
   * 当前 workspace 信息
   */
  currentWorkspace?: {
    id: string;
    name: string;
  };
  /**
   * 自定义样式类名
   */
  className?: string;
}

/**
 * Workspace 切换选择器组件
 * 显示当前 workspace 并提供下拉菜单切换到其他 workspace
 */
const WorkspaceSelector: React.FC<WorkspaceSelectorProps> = ({
  currentWorkspace,
  className = '',
}) => {
  const router = useRouter();
  const params = useParams();
  const currentWorkspaceId = params.workspace as string;
  
  const [workspaces, setWorkspaces] = useState<WorkspaceInfo[]>([]);
  const [currentWorkspaceInfo, setCurrentWorkspaceInfo] = useState<WorkspaceInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户的 workspace 列表
  const loadWorkspaces = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await getUserWorkspaces();

      if (result.status === 'success') {
        setWorkspaces(result.data);
      } else {
        setError(result.message || '获取工作空间列表失败');
        console.error('Failed to load workspaces:', result.message);
      }
    } catch (err) {
      setError('获取工作空间列表时发生错误');
      console.error('Error loading workspaces:', err);
    } finally {
      setLoading(false);
    }
  };

  // 获取当前 workspace 信息
  const loadCurrentWorkspaceInfo = useCallback(async () => {
    if (!currentWorkspaceId) return;

    try {
      const result = await getWorkspaceInfo(currentWorkspaceId);
      if (result.status === 'success' && result.data) {
        setCurrentWorkspaceInfo(result.data);
      }
    } catch (error) {
      console.error('Error loading current workspace info:', error);
    }
  }, [currentWorkspaceId]);

  // 当下拉菜单打开时加载 workspace 列表
  const handleDropdownOpenChange = (open: boolean) => {
    if (open && workspaces.length === 0) {
      loadWorkspaces();
    }
  };

  // 组件挂载时获取当前 workspace 信息
  useEffect(() => {
    loadCurrentWorkspaceInfo();
  }, [loadCurrentWorkspaceInfo]);

  // 切换到指定的 workspace
  const handleWorkspaceSwitch = (workspaceId: string) => {
    if (workspaceId !== currentWorkspaceId) {
      router.push(`/${workspaceId}/chat`);
    }
  };

  // 跳转到加入其他空间页面
  const handleJoinOtherWorkspace = () => {
    router.push('/workspaces');
  };

  // 构建下拉菜单项
  const menuItems: MenuProps['items'] = [
    // Workspace 列表
    ...workspaces.map((workspace) => ({
      key: workspace.id,
      label: (
        <div className="flex items-center justify-between py-2 px-3 min-w-56 hover:bg-gray-50 rounded-md transition-colors duration-150">
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-gray-900 truncate">
              {workspace.name}
            </div>
          </div>
          {workspace.id === currentWorkspaceId && (
            <CheckOutlined className="text-blue-500 ml-2 flex-shrink-0" />
          )}
        </div>
      ),
      onClick: () => handleWorkspaceSwitch(workspace.id),
      className: workspace.id === currentWorkspaceId ? 'bg-blue-50' : '',
    })),
    // 分割线
    ...(workspaces.length > 0 ? [{
      type: 'divider' as const,
    }] : []),
    // 加入其他空间选项
    {
      key: 'join-other',
      label: (
        <div className="flex items-center py-2 px-3 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors duration-150">
          <PlusOutlined className="mr-2 text-xs" />
          <span className="text-sm">加入其他工作空间</span>
        </div>
      ),
      onClick: handleJoinOtherWorkspace,
    },
  ];

  // 获取当前 workspace 的显示名称
  const getCurrentWorkspaceName = () => {
    // 优先使用传入的 currentWorkspace 信息
    if (currentWorkspace?.name) {
      return currentWorkspace.name;
    }

    // 其次使用从 API 获取的当前 workspace 信息
    if (currentWorkspaceInfo?.name) {
      return currentWorkspaceInfo.name;
    }

    // 再次尝试从 workspaces 列表中查找
    const current = workspaces.find(w => w.id === currentWorkspaceId);
    if (current?.name) {
      return current.name;
    }

    // 最后使用 workspaceId 或默认名称
    return currentWorkspaceId || '工作空间';
  };

  // 判断是否在 header 中使用
  const isInHeader = className.includes('workspace-in-header');

  return (
    <Dropdown
      menu={{ items: menuItems }}
      onOpenChange={handleDropdownOpenChange}
      trigger={['click']}
      
      placement="bottomLeft"
      overlayClassName="workspace-selector-dropdown"
      dropdownRender={(menu) => (
        <>
          {loading ? (
            <div className="flex items-center justify-center py-4 px-6">
              <Spin size="small" />
              <span className="ml-2 text-sm text-gray-500">加载中...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-4 px-6">
              <span className="text-sm text-red-500 mb-2">{error}</span>
              <Button
                size="small"
                type="link"
                onClick={loadWorkspaces}
                className="text-xs"
              >
                重试
              </Button>
            </div>
          ) : (
            menu
          )}
        </>
      )}
    >
      <div
        className={`flex items-center w-full text-left cursor-pointer transition-colors duration-200 ${
          isInHeader
            ? 'hover:bg-gray-200 rounded px-2 py-1 justify-start'
            : 'hover:bg-gray-200 rounded px-3 py-2 justify-between'
        } ${className}`}
      >
        <InteractionOutlined className={`flex-shrink-0 ${
          isInHeader ? 'text-xs mr-2' : 'text-sm mr-2'
        }`} />
        <div className="flex-1 min-w-0">
          <Text className={`font-medium text-gray-900 truncate block ${
            isInHeader ? 'text-base leading-tight' : 'text-sm'
          }`}>
            {getCurrentWorkspaceName()}
          </Text>
        </div>
        <DownOutlined className={`text-gray-400 flex-shrink-0 ${
          isInHeader ? 'text-xs ml-1' : 'text-xs ml-2'
        }`} />
      </div>
    </Dropdown>
  );
};

export default WorkspaceSelector;
