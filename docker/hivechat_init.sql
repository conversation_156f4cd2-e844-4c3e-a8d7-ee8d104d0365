--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: api_style; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.api_style AS ENUM (
    'openai',
    'openai_response',
    'claude',
    'gemini'
);


ALTER TYPE public.api_style OWNER TO postgres;

--
-- Name: avatar_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.avatar_type AS ENUM (
    'emoji',
    'url',
    'none'
);


ALTER TYPE public.avatar_type OWNER TO postgres;

--
-- Name: group_model_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.group_model_type AS ENUM (
    'all',
    'specific'
);


ALTER TYPE public.group_model_type OWNER TO postgres;

--
-- Name: history_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.history_type AS ENUM (
    'all',
    'count',
    'none'
);


ALTER TYPE public.history_type OWNER TO postgres;

--
-- Name: mcp_server_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.mcp_server_type AS ENUM (
    'sse',
    'streamableHttp'
);


ALTER TYPE public.mcp_server_type OWNER TO postgres;

--
-- Name: message_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.message_type AS ENUM (
    'text',
    'image',
    'error',
    'break'
);


ALTER TYPE public.message_type OWNER TO postgres;

--
-- Name: model_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.model_type AS ENUM (
    'default',
    'custom'
);


ALTER TYPE public.model_type OWNER TO postgres;

--
-- Name: plan_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.plan_type AS ENUM (
    'free',
    'plus',
    'pro'
);


ALTER TYPE public.plan_type OWNER TO postgres;

--
-- Name: provider_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.provider_type AS ENUM (
    'default',
    'custom'
);


ALTER TYPE public.provider_type OWNER TO postgres;

--
-- Name: search_status; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.search_status AS ENUM (
    'none',
    'searching',
    'error',
    'done'
);


ALTER TYPE public.search_status OWNER TO postgres;

--
-- Name: token_limit_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.token_limit_type AS ENUM (
    'unlimited',
    'limited'
);


ALTER TYPE public.token_limit_type OWNER TO postgres;

--
-- Name: user_role_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.user_role_type AS ENUM (
    'owner',
    'admin',
    'member'
);


ALTER TYPE public.user_role_type OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: account; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.account (
    "userId" text NOT NULL,
    type text NOT NULL,
    provider text NOT NULL,
    "providerAccountId" text NOT NULL,
    refresh_token text,
    access_token text,
    expires_at integer,
    token_type text,
    scope text,
    id_token text,
    session_state text
);


ALTER TABLE public.account OWNER TO postgres;

--
-- Name: app_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.app_settings (
    id integer NOT NULL,
    key text NOT NULL,
    value text,
    workspace_id character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.app_settings OWNER TO postgres;

--
-- Name: app_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.app_settings ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.app_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: authenticator; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.authenticator (
    "credentialID" text NOT NULL,
    "userId" text NOT NULL,
    "providerAccountId" text NOT NULL,
    "credentialPublicKey" text NOT NULL,
    counter integer NOT NULL,
    "credentialDeviceType" text NOT NULL,
    "credentialBackedUp" boolean NOT NULL,
    transports text
);


ALTER TABLE public.authenticator OWNER TO postgres;

--
-- Name: bots; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.bots (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    "desc" text,
    prompt text,
    avatar_type public.avatar_type DEFAULT 'none'::public.avatar_type NOT NULL,
    avatar character varying,
    source_url character varying,
    creator character varying,
    workspace_id character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    delete_at timestamp without time zone
);


ALTER TABLE public.bots OWNER TO postgres;

--
-- Name: bots_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.bots ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.bots_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: chats; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.chats (
    id text NOT NULL,
    "userId" text,
    title character varying(255) NOT NULL,
    history_type public.history_type DEFAULT 'count'::public.history_type NOT NULL,
    history_count integer DEFAULT 5 NOT NULL,
    search_enabled boolean DEFAULT false,
    default_model character varying,
    default_provider character varying,
    is_star boolean DEFAULT false,
    is_with_bot boolean DEFAULT false,
    bot_id integer,
    avatar character varying,
    avatar_type public.avatar_type DEFAULT 'none'::public.avatar_type NOT NULL,
    prompt text,
    star_at timestamp without time zone,
    input_tokens integer DEFAULT 0 NOT NULL,
    output_tokens integer DEFAULT 0 NOT NULL,
    total_tokens integer DEFAULT 0 NOT NULL,
    workspace_id character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.chats OWNER TO postgres;

--
-- Name: global_app_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.global_app_settings (
    key text NOT NULL,
    value text,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.global_app_settings OWNER TO postgres;

--
-- Name: group_models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.group_models (
    "groupId" text NOT NULL,
    "modelId" integer NOT NULL
);


ALTER TABLE public.group_models OWNER TO postgres;

--
-- Name: groups; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.groups (
    id text NOT NULL,
    name text NOT NULL,
    model_type public.group_model_type DEFAULT 'all'::public.group_model_type NOT NULL,
    token_limit_type public.token_limit_type DEFAULT 'unlimited'::public.token_limit_type NOT NULL,
    monthly_token_limit integer,
    is_default boolean DEFAULT false,
    workspace_id character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.groups OWNER TO postgres;

--
-- Name: llm_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.llm_settings (
    id integer NOT NULL,
    provider character varying(255) NOT NULL,
    "providerName" character varying(255) NOT NULL,
    apikey character varying(255),
    endpoint character varying(1024),
    is_active boolean DEFAULT false,
    api_style public.api_style DEFAULT 'openai'::public.api_style NOT NULL,
    type public.provider_type DEFAULT 'default'::public.provider_type NOT NULL,
    logo character varying(2048),
    "order" integer,
    workspace_id character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.llm_settings OWNER TO postgres;

--
-- Name: llm_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.llm_settings ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.llm_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: mcp_servers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.mcp_servers (
    id uuid NOT NULL,
    name text NOT NULL,
    description text,
    type public.mcp_server_type DEFAULT 'sse'::public.mcp_server_type,
    base_url text NOT NULL,
    workspace_id character varying(255) NOT NULL,
    is_active boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.mcp_servers OWNER TO postgres;

--
-- Name: mcp_tools; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.mcp_tools (
    id uuid NOT NULL,
    name text NOT NULL,
    server_id uuid NOT NULL,
    description text,
    input_schema text NOT NULL
);


ALTER TABLE public.mcp_tools OWNER TO postgres;

--
-- Name: messages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.messages (
    id integer NOT NULL,
    "userId" text NOT NULL,
    "chatId" text NOT NULL,
    workspace_id character varying(255) NOT NULL,
    role character varying(255) NOT NULL,
    content json,
    reasonin_content text,
    model character varying(255),
    "providerId" character varying(255) NOT NULL,
    message_type character varying DEFAULT 'text'::character varying NOT NULL,
    search_enabled boolean DEFAULT false,
    web_search json,
    search_status public.search_status DEFAULT 'none'::public.search_status NOT NULL,
    mcp_tools json,
    input_tokens integer,
    output_tokens integer,
    total_tokens integer,
    error_type character varying,
    error_message character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    delete_at timestamp without time zone
);


ALTER TABLE public.messages OWNER TO postgres;

--
-- Name: messages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.messages ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.messages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.models (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    "displayName" character varying(255) NOT NULL,
    "maxTokens" integer,
    support_vision boolean DEFAULT false,
    support_tool boolean DEFAULT false,
    built_in_image_gen boolean DEFAULT false,
    built_in_web_search boolean DEFAULT false,
    selected boolean DEFAULT true,
    "providerId" character varying(255) NOT NULL,
    "providerName" character varying(255) NOT NULL,
    type public.model_type DEFAULT 'default'::public.model_type NOT NULL,
    "order" integer DEFAULT 1,
    workspace_id character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.models OWNER TO postgres;

--
-- Name: models_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.models ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.models_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: search_engine_config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.search_engine_config (
    id text NOT NULL,
    name text NOT NULL,
    api_key text,
    max_results integer DEFAULT 5 NOT NULL,
    extract_keywords boolean DEFAULT false NOT NULL,
    workspace_id character varying(255) NOT NULL,
    is_active boolean DEFAULT false NOT NULL
);


ALTER TABLE public.search_engine_config OWNER TO postgres;

--
-- Name: session; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.session (
    "sessionToken" text NOT NULL,
    "userId" text NOT NULL,
    expires timestamp without time zone NOT NULL
);


ALTER TABLE public.session OWNER TO postgres;

--
-- Name: usage_report; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.usage_report (
    date date NOT NULL,
    user_id text,
    model_id character varying(255),
    provider_id character varying(255),
    input_tokens integer DEFAULT 0 NOT NULL,
    output_tokens integer DEFAULT 0 NOT NULL,
    total_tokens integer DEFAULT 0 NOT NULL,
    workspace_id character varying(255) NOT NULL
);


ALTER TABLE public.usage_report OWNER TO postgres;

--
-- Name: user; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."user" (
    id text NOT NULL,
    name text,
    email text,
    password text,
    "dingdingUnionId" text,
    "wecomUserId" text,
    "feishuUserId" text,
    "feishuOpenId" text,
    "feishuUnionId" text,
    "emailVerified" timestamp without time zone,
    "isAdmin" boolean DEFAULT false,
    image text,
    created_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public."user" OWNER TO postgres;

--
-- Name: user_workspace; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_workspace (
    id text NOT NULL,
    user_id text,
    workspace_id character varying(255) NOT NULL,
    email text,
    role public.user_role_type DEFAULT 'member'::public.user_role_type NOT NULL,
    is_active boolean DEFAULT true,
    joined_at timestamp without time zone DEFAULT now(),
    "groupId" text,
    today_total_tokens integer DEFAULT 0 NOT NULL,
    current_month_total_tokens integer DEFAULT 0 NOT NULL,
    usage_updated_at timestamp without time zone DEFAULT now() NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.user_workspace OWNER TO postgres;

--
-- Name: verificationToken; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."verificationToken" (
    identifier text NOT NULL,
    token text NOT NULL,
    expires timestamp without time zone NOT NULL
);


ALTER TABLE public."verificationToken" OWNER TO postgres;

--
-- Name: workspace_invites; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.workspace_invites (
    id uuid NOT NULL,
    workspace_id character varying(255) NOT NULL,
    invite_code character varying(255) NOT NULL,
    created_by text NOT NULL,
    expires_at timestamp without time zone NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.workspace_invites OWNER TO postgres;

--
-- Name: workspaces; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.workspaces (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    owner text,
    plan public.plan_type DEFAULT 'free'::public.plan_type NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.workspaces OWNER TO postgres;

--
-- Data for Name: account; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.account ("userId", type, provider, "providerAccountId", refresh_token, access_token, expires_at, token_type, scope, id_token, session_state) FROM stdin;
\.


--
-- Data for Name: app_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.app_settings (id, key, value, workspace_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: authenticator; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.authenticator ("credentialID", "userId", "providerAccountId", "credentialPublicKey", counter, "credentialDeviceType", "credentialBackedUp", transports) FROM stdin;
\.


--
-- Data for Name: bots; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.bots (id, title, "desc", prompt, avatar_type, avatar, source_url, creator, workspace_id, created_at, updated_at, delete_at) FROM stdin;
\.


--
-- Data for Name: chats; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.chats (id, "userId", title, history_type, history_count, search_enabled, default_model, default_provider, is_star, is_with_bot, bot_id, avatar, avatar_type, prompt, star_at, input_tokens, output_tokens, total_tokens, workspace_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: global_app_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.global_app_settings (key, value, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: group_models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.group_models ("groupId", "modelId") FROM stdin;
\.


--
-- Data for Name: groups; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.groups (id, name, model_type, token_limit_type, monthly_token_limit, is_default, workspace_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: llm_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.llm_settings (id, provider, "providerName", apikey, endpoint, is_active, api_style, type, logo, "order", workspace_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: mcp_servers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.mcp_servers (id, name, description, type, base_url, workspace_id, is_active, created_at) FROM stdin;
\.


--
-- Data for Name: mcp_tools; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.mcp_tools (id, name, server_id, description, input_schema) FROM stdin;
\.


--
-- Data for Name: messages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.messages (id, "userId", "chatId", workspace_id, role, content, reasonin_content, model, "providerId", message_type, search_enabled, web_search, search_status, mcp_tools, input_tokens, output_tokens, total_tokens, error_type, error_message, created_at, updated_at, delete_at) FROM stdin;
\.


--
-- Data for Name: models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.models (id, name, "displayName", "maxTokens", support_vision, support_tool, built_in_image_gen, built_in_web_search, selected, "providerId", "providerName", type, "order", workspace_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: search_engine_config; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.search_engine_config (id, name, api_key, max_results, extract_keywords, workspace_id, is_active) FROM stdin;
\.


--
-- Data for Name: session; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.session ("sessionToken", "userId", expires) FROM stdin;
\.


--
-- Data for Name: usage_report; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.usage_report (date, user_id, model_id, provider_id, input_tokens, output_tokens, total_tokens, workspace_id) FROM stdin;
\.


--
-- Data for Name: user; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."user" (id, name, email, password, "dingdingUnionId", "wecomUserId", "feishuUserId", "feishuOpenId", "feishuUnionId", "emailVerified", "isAdmin", image, created_at) FROM stdin;
\.


--
-- Data for Name: user_workspace; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_workspace (id, user_id, workspace_id, email, role, is_active, joined_at, "groupId", today_total_tokens, current_month_total_tokens, usage_updated_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: verificationToken; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."verificationToken" (identifier, token, expires) FROM stdin;
\.


--
-- Data for Name: workspace_invites; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.workspace_invites (id, workspace_id, invite_code, created_by, expires_at, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: workspaces; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.workspaces (id, name, owner, plan, created_at, updated_at) FROM stdin;
\.


--
-- Name: app_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.app_settings_id_seq', 1, false);


--
-- Name: bots_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.bots_id_seq', 1, false);


--
-- Name: llm_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.llm_settings_id_seq', 1, false);


--
-- Name: messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.messages_id_seq', 1, false);


--
-- Name: models_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.models_id_seq', 1, false);


--
-- Name: app_settings app_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.app_settings
    ADD CONSTRAINT app_settings_pkey PRIMARY KEY (id);


--
-- Name: authenticator authenticator_credentialID_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.authenticator
    ADD CONSTRAINT "authenticator_credentialID_unique" UNIQUE ("credentialID");


--
-- Name: bots bots_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.bots
    ADD CONSTRAINT bots_pkey PRIMARY KEY (id);


--
-- Name: chats chats_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chats
    ADD CONSTRAINT chats_pkey PRIMARY KEY (id);


--
-- Name: global_app_settings global_app_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.global_app_settings
    ADD CONSTRAINT global_app_settings_pkey PRIMARY KEY (key);


--
-- Name: groups groups_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.groups
    ADD CONSTRAINT groups_pkey PRIMARY KEY (id);


--
-- Name: llm_settings llm_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.llm_settings
    ADD CONSTRAINT llm_settings_pkey PRIMARY KEY (id);


--
-- Name: mcp_servers mcp_servers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mcp_servers
    ADD CONSTRAINT mcp_servers_pkey PRIMARY KEY (id);


--
-- Name: mcp_tools mcp_tools_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mcp_tools
    ADD CONSTRAINT mcp_tools_pkey PRIMARY KEY (id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id);


--
-- Name: models models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_pkey PRIMARY KEY (id);


--
-- Name: search_engine_config search_engine_config_id_workspace_id_pk; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.search_engine_config
    ADD CONSTRAINT search_engine_config_id_workspace_id_pk PRIMARY KEY (id, workspace_id);


--
-- Name: session session_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.session
    ADD CONSTRAINT session_pkey PRIMARY KEY ("sessionToken");


--
-- Name: app_settings unique_key_workspace; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.app_settings
    ADD CONSTRAINT unique_key_workspace UNIQUE (key, workspace_id);


--
-- Name: mcp_servers unique_mcp_server_name_workspace; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mcp_servers
    ADD CONSTRAINT unique_mcp_server_name_workspace UNIQUE (name, workspace_id);


--
-- Name: models unique_model_provider; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT unique_model_provider UNIQUE (name, "providerId", workspace_id);


--
-- Name: llm_settings unique_provider_workspace; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.llm_settings
    ADD CONSTRAINT unique_provider_workspace UNIQUE (provider, workspace_id);


--
-- Name: user user_email_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT user_email_unique UNIQUE (email);


--
-- Name: user user_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT user_pkey PRIMARY KEY (id);


--
-- Name: user_workspace user_workspace_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_workspace
    ADD CONSTRAINT user_workspace_pkey PRIMARY KEY (id);


--
-- Name: workspace_invites workspace_invites_invite_code_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.workspace_invites
    ADD CONSTRAINT workspace_invites_invite_code_unique UNIQUE (invite_code);


--
-- Name: workspace_invites workspace_invites_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.workspace_invites
    ADD CONSTRAINT workspace_invites_pkey PRIMARY KEY (id);


--
-- Name: workspaces workspaces_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.workspaces
    ADD CONSTRAINT workspaces_pkey PRIMARY KEY (id);


--
-- Name: account account_userId_user_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.account
    ADD CONSTRAINT "account_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON DELETE CASCADE;


--
-- Name: app_settings app_settings_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.app_settings
    ADD CONSTRAINT app_settings_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: authenticator authenticator_userId_user_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.authenticator
    ADD CONSTRAINT "authenticator_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON DELETE CASCADE;


--
-- Name: bots bots_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.bots
    ADD CONSTRAINT bots_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: chats chats_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chats
    ADD CONSTRAINT chats_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: group_models group_models_groupId_groups_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.group_models
    ADD CONSTRAINT "group_models_groupId_groups_id_fk" FOREIGN KEY ("groupId") REFERENCES public.groups(id) ON DELETE CASCADE;


--
-- Name: group_models group_models_modelId_models_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.group_models
    ADD CONSTRAINT "group_models_modelId_models_id_fk" FOREIGN KEY ("modelId") REFERENCES public.models(id) ON DELETE CASCADE;


--
-- Name: groups groups_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.groups
    ADD CONSTRAINT groups_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: llm_settings llm_settings_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.llm_settings
    ADD CONSTRAINT llm_settings_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: mcp_servers mcp_servers_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mcp_servers
    ADD CONSTRAINT mcp_servers_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: mcp_tools mcp_tools_server_id_mcp_servers_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mcp_tools
    ADD CONSTRAINT mcp_tools_server_id_mcp_servers_id_fk FOREIGN KEY (server_id) REFERENCES public.mcp_servers(id) ON DELETE CASCADE;


--
-- Name: messages messages_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: models models_provider_workspace_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_provider_workspace_fk FOREIGN KEY ("providerId", workspace_id) REFERENCES public.llm_settings(provider, workspace_id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: models models_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: search_engine_config search_engine_config_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.search_engine_config
    ADD CONSTRAINT search_engine_config_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: session session_userId_user_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.session
    ADD CONSTRAINT "session_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON DELETE CASCADE;


--
-- Name: usage_report usage_report_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.usage_report
    ADD CONSTRAINT usage_report_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: user_workspace user_workspace_user_id_user_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_workspace
    ADD CONSTRAINT user_workspace_user_id_user_id_fk FOREIGN KEY (user_id) REFERENCES public."user"(id) ON DELETE CASCADE;


--
-- Name: user_workspace user_workspace_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_workspace
    ADD CONSTRAINT user_workspace_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- Name: workspace_invites workspace_invites_created_by_user_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.workspace_invites
    ADD CONSTRAINT workspace_invites_created_by_user_id_fk FOREIGN KEY (created_by) REFERENCES public."user"(id) ON DELETE CASCADE;


--
-- Name: workspace_invites workspace_invites_workspace_id_workspaces_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.workspace_invites
    ADD CONSTRAINT workspace_invites_workspace_id_workspaces_id_fk FOREIGN KEY (workspace_id) REFERENCES public.workspaces(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

