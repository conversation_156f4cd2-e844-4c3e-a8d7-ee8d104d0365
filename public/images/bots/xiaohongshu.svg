<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
[
<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:layerName CDATA #IMPLIED >
<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:mask CDATA #IMPLIED >
<!ATTLIST text
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:width CDATA #IMPLIED >
<!ATTLIST text
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:text CDATA #IMPLIED >

<!ATTLIST path
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:blendMode CDATA #IMPLIED >
<!ATTLIST path
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowColor CDATA #IMPLIED >
<!ATTLIST path
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowOpacity CDATA #IMPLIED >
<!ATTLIST path
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowRadius CDATA #IMPLIED >
<!ATTLIST path
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowOffset CDATA #IMPLIED >
<!ATTLIST path
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowAngle CDATA #IMPLIED >

<!ATTLIST image
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:blendMode CDATA #IMPLIED >
<!ATTLIST image
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowColor CDATA #IMPLIED >
<!ATTLIST image
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowOpacity CDATA #IMPLIED >
<!ATTLIST image
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowRadius CDATA #IMPLIED >
<!ATTLIST image
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowOffset CDATA #IMPLIED >
<!ATTLIST image
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowAngle CDATA #IMPLIED >

<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:blendMode CDATA #IMPLIED >
<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowColor CDATA #IMPLIED >
<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowOpacity CDATA #IMPLIED >
<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowRadius CDATA #IMPLIED >
<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowOffset CDATA #IMPLIED >
<!ATTLIST g
  xmlns:vectornator CDATA #FIXED "http://vectornator.io"
  vectornator:shadowAngle CDATA #IMPLIED >

<!ENTITY % SVG.filter.extra.content  "| feDropShadow" >
<!ELEMENT feDropShadow EMPTY>
<!ATTLIST feDropShadow
  xmlns:svg CDATA #FIXED "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
  dx CDATA #IMPLIED>
<!ATTLIST feDropShadow
  xmlns:svg CDATA #FIXED "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
  dy CDATA #IMPLIED>
<!ATTLIST feDropShadow
  xmlns:svg CDATA #FIXED "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
  stdDeviation CDATA #IMPLIED>
<!ATTLIST feDropShadow
  xmlns:svg CDATA #FIXED "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
  flood-opacity CDATA #IMPLIED>
<!ATTLIST feDropShadow
  xmlns:svg CDATA #FIXED "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
  in CDATA #IMPLIED>
<!ATTLIST feDropShadow
  xmlns:svg CDATA #FIXED "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
  result CDATA #IMPLIED>
<!ATTLIST feDropShadow
  xmlns:svg CDATA #FIXED "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"
  flood-color CDATA #IMPLIED>
]
>
<!-- Created with Vectornator (http://vectornator.io/) -->
<svg height="100%" stroke-miterlimit="10" style="fill-rule:nonzero;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;" version="1.1" viewBox="0 0 1024 1024" width="100%" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:vectornator="http://vectornator.io" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs/>
<clipPath id="ArtboardFrame">
<rect height="1024" width="1024" x="0" y="0"/>
</clipPath>
<g clip-path="url(#ArtboardFrame)" id="Untitled" vectornator:layerName="Untitled">
<path d="M1024.74 0.56872L-0.864455 0.0265403L0.887204 1023.65L1024.2 1023.86" fill="#ff2442" fill-rule="evenodd" opacity="1" stroke="none"/>
<path d="M726.521 366.364L783.865 366.364L783.865 386.899C783.865 388.537 784.666 389.302 786.232 389.266C820.238 388.246 854.498 389.338 872.048 424.218C882.497 444.898 880.349 476.356 879.766 501.041C879.73 502.497 880.422 503.298 881.805 503.444C885.81 503.808 889.706 504.172 893.492 504.645C961.104 512.765 947.742 576.517 947.96 626.615C948.069 644.091 946.103 656.871 942.135 665.027C933.761 681.92 918.76 691.605 897.133 694.008L854.972 694.008L833.418 643.982C833.199 643.486 833.24 642.913 833.527 642.453C833.812 642.003 834.305 641.729 834.838 641.725L880.567 641.688C883.116 641.688 885.519 640.596 887.266 638.703C889.034 636.794 889.999 634.278 889.961 631.676C889.742 616.384 889.633 601.129 889.706 585.874C889.706 572.147 883.225 565.121 870.154 564.756C855.372 564.392 827.374 564.392 786.123 564.829C784.666 564.829 783.938 565.63 783.938 567.196L783.72 694.008L726.448 694.008L726.266 566.65C726.286 566.045 726.062 565.457 725.644 565.019C725.227 564.581 724.651 564.329 724.045 564.32L670.524 564.32C669.145 564.28 668.048 563.151 668.049 561.771L668.121 506.357C668.121 504.5 668.995 503.553 670.743 503.553L723.645 503.662C724.336 503.657 724.995 503.367 725.465 502.861C725.939 502.345 726.199 501.669 726.193 500.968L726.193 453.054C726.234 451.318 724.87 449.873 723.135 449.814L690.476 449.959C688.765 449.959 687.928 449.049 687.928 447.265L687.819 391.559C687.819 389.921 688.547 389.12 690.185 389.12L724.009 389.12C725.465 389.12 726.194 388.392 726.194 386.826L726.558 366.364L726.521 366.364ZM785.904 503.735L821.476 503.662C822.058 503.662 822.604 503.408 823.005 502.971C823.411 502.537 823.633 501.963 823.624 501.369L823.442 456.841C823.442 453.345 820.893 450.505 817.798 450.505L789.254 450.578C787.708 450.601 786.248 451.292 785.249 452.471C784.164 453.737 783.581 455.356 783.61 457.023L783.792 501.551C783.792 502.789 784.775 503.735 785.904 503.735ZM417.956 507.74C404.12 507.995 379.108 511.854 373.646 494.05C370.333 483.419 377.833 468.601 382.384 458.224C395.346 428.696 408.053 399.06 420.541 369.314C421.051 368.112 421.924 367.493 423.162 367.493L477.885 367.493C478.358 367.493 478.759 367.748 478.977 368.148C479.232 368.537 479.286 369.024 479.123 369.459L447.447 443.478C446.719 445.19 446.901 447.119 447.848 448.721C448.778 450.246 450.43 451.182 452.217 451.197L499.111 451.197C499.694 451.197 500.204 451.488 500.531 451.962C500.822 452.471 500.895 453.054 500.64 453.6C487.096 485.167 473.589 516.515 460.117 547.644C458.77 550.739 458.188 553.033 458.406 554.489C458.879 557.657 460.663 559.259 463.722 559.295L493.395 559.477C495.106 559.514 495.652 560.351 494.961 562.026L475.773 607.173C475.226 608.706 473.76 609.717 472.132 609.685C441.986 610.049 420.905 610.049 408.89 609.503C389.011 608.593 384.132 591.189 391.851 573.24L419.121 509.597C419.282 509.19 419.242 508.731 419.012 508.359C418.787 507.97 418.369 507.733 417.919 507.74L417.956 507.74ZM190.582 694.008L169.101 694.008L148.057 644.601C147.844 644.116 147.885 643.557 148.166 643.108C148.422 642.665 148.892 642.388 149.404 642.38L179.114 642.307C182.955 642.228 186.003 639.049 185.922 635.208L186.723 373.173C186.703 372.491 186.958 371.829 187.43 371.336C187.903 370.844 188.553 370.561 189.235 370.552L240.353 370.552C242.756 370.552 243.958 371.826 243.994 374.338C244.213 463.066 244.213 550.266 243.994 635.972C243.849 671.143 227.537 695.173 190.582 694.008Z" fill="#ffffff" fill-rule="evenodd" opacity="1" stroke="none"/>
<path d="M670.087 694.008L476.174 694.008L502.17 635.39C502.702 633.997 504.066 633.102 505.556 633.169L553.033 633.242C554.708 633.242 555.581 632.404 555.581 630.693L555.581 452.836C555.581 451.306 554.853 450.542 553.397 450.542L521.903 450.505C520.483 450.505 519.355 449.268 519.355 447.775L519.355 390.722C519.355 389.848 520.01 389.12 520.847 389.12L649.225 389.12C650.827 389.12 651.592 389.957 651.592 391.632L651.665 448.066C651.665 449.704 650.864 450.542 649.262 450.542L617.513 450.542C616.057 450.542 615.328 451.306 615.328 452.836L615.328 630.584C615.328 632.295 616.166 633.132 617.768 633.132L668.085 633.242C669.468 633.242 670.16 633.97 670.16 635.426L670.087 694.044L670.087 694.008ZM901.029 394.654C940.642 367.42 968.531 436.852 925.132 448.758C918.068 450.724 906.818 450.833 891.417 449.122C890.034 448.976 889.378 448.212 889.378 446.755C889.16 430.371 885.919 405.031 901.029 394.691L901.029 394.654ZM354.204 598.799L327.989 659.856C325.623 665.318 323.038 665.427 320.162 660.293C300.865 625.414 294.311 596.942 290.525 553.579C287.612 519.901 285.1 486.222 282.915 452.471C282.842 450.942 283.534 450.178 284.991 450.178L338.111 450.214C339.604 450.214 340.441 451.015 340.551 452.544C343.281 491.793 346.158 530.933 349.143 569.963C349.908 580.012 351.619 588.349 354.24 594.976C354.766 596.199 354.753 597.586 354.204 598.799ZM75.0933 596.541L75.0933 594.029C77.5893 590.64 79.2146 586.689 79.8265 582.524C83.7586 539.197 86.9626 495.907 89.4748 452.581C89.5841 451.234 90.2394 450.542 91.5137 450.542L145.763 450.542C146.236 450.542 146.71 450.76 147.074 451.161C147.401 451.525 147.583 452.035 147.547 452.544C144.687 492.462 141.495 532.355 137.972 572.22C135.423 601.165 126.175 639.904 106.842 663.388C105.604 664.881 104.548 664.735 103.747 662.915L75.0933 596.541ZM445.08 694.008L366.51 694.008L356.498 690.039C355.078 689.493 354.677 688.51 355.333 687.09L379.981 630.657C380.71 629.018 381.875 628.399 383.549 628.836C410.492 636.154 441.694 633.132 469.256 633.242C470.967 633.278 471.441 634.115 470.712 635.717L445.08 693.972L445.08 694.008Z" fill="#ffffff" fill-rule="evenodd" opacity="1" stroke="none"/>
</g>
</svg>
